#!/bin/bash

echo "=== SOCKS代理服务器配置完成 ==="
echo ""
echo "服务器信息："
SERVER_IP=$(curl -s http://ipinfo.io/ip)
echo "- 服务器IP: $SERVER_IP"
echo "- SOCKS端口: 80"
echo "- 协议: SOCKS5"
echo "- 认证方式: 无需密码（也支持用户名密码）"
echo ""
echo "认证信息（可选）："
echo "- 用户名: socksuser"
echo "- 密码: SocksPass2024!"
echo ""
echo "测试SOCKS代理连接："
echo "1. 无认证测试（访问百度）："
if curl --socks5 127.0.0.1:80 --connect-timeout 10 -s http://www.baidu.com | grep -q "百度"; then
    echo "✓ 无密码SOCKS代理连接成功"
else
    echo "✗ 无密码SOCKS代理连接失败"
fi
echo ""
echo "2. 端口连通性测试："
if timeout 5 bash -c "</dev/tcp/$SERVER_IP/80" 2>/dev/null; then
    echo "✓ 外部端口80连接正常"
else
    echo "✗ 外部端口80连接失败"
fi
echo ""
echo "3. 服务状态："
systemctl is-active sockd && echo "✓ SOCKS服务运行正常" || echo "✗ SOCKS服务未运行"
echo ""
echo "4. 端口监听状态："
netstat -tlnp | grep :80 && echo "✓ 端口80监听正常" || echo "✗ 端口80未监听"
echo ""
echo "客户端配置示例："
echo "方式1 - 无密码连接："
echo "- 代理类型: SOCKS5"
echo "- 代理服务器: $SERVER_IP"
echo "- 代理端口: 80"
echo "- 认证: 无"
echo ""
echo "方式2 - 用户名密码连接："
echo "- 代理类型: SOCKS5"
echo "- 代理服务器: $SERVER_IP"
echo "- 代理端口: 80"
echo "- 用户名: socksuser"
echo "- 密码: SocksPass2024!"
